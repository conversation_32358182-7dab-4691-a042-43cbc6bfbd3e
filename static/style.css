/* Import CLI-style fonts */
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300..700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Code+Pro:ital,wght@0,200..900;1,200..900&display=swap');

/* Modern Dark Theme - Inspired by Financial Dashboard with CLI Aesthetics */
:root {
    /* Primary Green Palette */
    --primary-500: #2E8B57;
    --primary-400: #4CAF50;
    --primary-300: #66BB6A;
    --primary-200: #81C784;
    --primary-100: #A5D6A7;

    /* Background Colors */
    --bg-primary: #0F0F0F;
    --bg-secondary: #1A1A1A;
    --bg-tertiary: #242424;
    --bg-card: #1E1E1E;
    --bg-card-hover: #252525;

    /* Text Colors */
    --text-primary: #FFFFFF;
    --text-secondary: #B3B3B3;
    --text-muted: #808080;
    --text-success: #4CAF50;
    --text-error: #F44336;

    /* Border & Divider */
    --border-primary: #2A2A2A;
    --border-secondary: #333333;
    --border-accent: #4CAF50;

    /* Shadows */
    --shadow-card: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-card-hover: 0 8px 24px rgba(0, 0, 0, 0.5);
    --shadow-primary: 0 4px 12px rgba(76, 175, 80, 0.3);

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-2xl: 24px;

    /* Spacing */
    --space-xs: 4px;
    --space-sm: 8px;
    --space-md: 16px;
    --space-lg: 24px;
    --space-xl: 32px;
    --space-2xl: 48px;

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);

    /* CLI-style Fonts */
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Source Code Pro', 'Consolas', 'Monaco', 'Courier New', monospace;
    --font-primary: var(--font-mono);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
    font-weight: 400;
    letter-spacing: 0.025em;
}

.app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 100vw;
    overflow: hidden;
    background: var(--bg-primary);
}

/* ========================================
   SIDEBAR STYLES - PREMIUM MODERN DESIGN
   ======================================== */

/* Sidebar Container */
.sidebar {
    position: fixed;
    left: -280px;
    top: 90px;
    width: 280px;
    height: calc(100vh - 100px);
    background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 100;
    transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    backdrop-filter: blur(20px);
    box-shadow: 4px 0 24px rgba(0, 0, 0, 0.3);
}

.sidebar.open {
    left: 0;
}

/* Sidebar Backdrop */
.sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.6);
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

.sidebar-backdrop.active {
    opacity: 1;
    visibility: visible;
}

/* Sidebar Header */
.sidebar-header {
    background: rgba(255, 255, 255, 0.02);
    padding: 20px 20px 16px 20px;
    flex-shrink: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.3), transparent);
}

.sidebar-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 15px;
    font-weight: 600;
    text-align: left;
    opacity: 0.9;
    letter-spacing: 0.3px;
}

/* Sidebar Content */
.sidebar-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
    overflow-y: auto;
    background: transparent;
}

.sidebar-content::-webkit-scrollbar {
    width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* New Chat Button */
.new-chat-btn {
    width: 100%;
    padding: 12px 16px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.new-chat-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.new-chat-btn:hover::before {
    left: 100%;
}

.new-chat-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.new-chat-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

/* Chat Sessions List */
.chat-sessions-list {
    flex: 1;
    overflow-y: auto;
}

.chat-sessions-list h4 {
    color: rgba(255, 255, 255, 0.5);
    font-size: 11px;
    font-weight: 600;
    margin: 0 0 12px 4px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Chat Sessions Container */
.chat-sessions {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* Individual Chat Session */
.chat-session {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 12px 14px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.chat-session::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: transparent;
    transition: all 0.2s ease;
}

.chat-session:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    transform: translateX(2px);
}

.chat-session:hover::before {
    background: rgba(76, 175, 80, 0.5);
}

.chat-session.active {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(76, 175, 80, 0.08) 100%);
    border-color: rgba(76, 175, 80, 0.3);
    transform: translateX(0);
}

.chat-session.active::before {
    background: #4CAF50;
    width: 3px;
}

.chat-session-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 6px 0;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-session.active .chat-session-title {
    color: #ffffff;
    font-weight: 600;
}

.chat-session-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
}

.chat-session.active .chat-session-meta {
    color: rgba(255, 255, 255, 0.7);
}

.chat-session-date {
    font-size: 11px;
}

.chat-session-count {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 10px;
    font-weight: 500;
}

.chat-session.active .chat-session-count {
    background: rgba(76, 175, 80, 0.3);
    color: #ffffff;
}

/* Sidebar Responsive Behavior */
@media (min-width: 768px) {

    /* Desktop: Sidebar can push content */
    .chat-container.sidebar-open {
        margin-left: 280px;
        transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .sidebar-backdrop {
        display: none;
    }
}

@media (max-width: 767px) {

    /* Mobile: Sidebar overlays content */
    .sidebar {
        width: 260px;
        left: -260px;
    }

    .chat-container.sidebar-open {
        margin-left: 0;
    }

    .sidebar-backdrop.active {
        display: block;
    }
}

/* Empty State for Sessions */
.chat-sessions-empty {
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.3);
    font-size: 13px;
    border: 1px dashed rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    margin: 20px 0;
}

.chat-sessions-empty::before {
    content: "💬";
    display: block;
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.3;
    filter: grayscale(1);
}

/* Header */
.header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--space-lg) var(--space-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 50;
    box-shadow: var(--shadow-card);
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.sidebar-toggle {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    font-size: 16px;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.sidebar-toggle:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    transform: scale(1.05);
}

.header h1 {
    font-family: var(--font-primary);
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.header-controls {
    display: flex;
    gap: var(--space-md);
    align-items: center;
}

.model-selector-wrapper {
    position: relative;
}

.model-select {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-sm) var(--space-md);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: 14px;
    min-width: 200px;
    transition: var(--transition-fast);
    letter-spacing: 0.025em;
}

.model-select:focus {
    outline: none;
    border-color: var(--border-accent);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.model-select option {
    background: var(--bg-card);
    color: var(--text-primary);
}

.mcp-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-sm) var(--space-lg);
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.mcp-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-secondary);
    transform: translateY(-1px);
}

.new-chat-btn {
    background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    padding: var(--space-sm) var(--space-lg);
    transition: var(--transition-fast);
    box-shadow: var(--shadow-primary);
}

.new-chat-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
}

.chat-sessions {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    max-height: 400px;
    overflow-y: auto;
}

/* Context Status */
.context-status {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 12px;
    color: var(--text-secondary);
    background: var(--bg-card);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
}

.context-bar {
    width: 60px;
    height: 6px;
    background: var(--border-primary);
    border-radius: 3px;
    overflow: hidden;
}

.context-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-400) 0%, var(--primary-300) 70%, #ef4444 90%);
    transition: width var(--transition-normal);
    width: 0%;
    border-radius: 3px;
}

.context-text {
    font-weight: 500;
    min-width: 50px;
    color: var(--text-primary);
}

/* Context Warning */
.context-warning {
    background: linear-gradient(135deg, #fef3c7, #fbbf24);
    border: 1px solid #f59e0b;
    border-radius: var(--radius-lg);
    margin: var(--space-lg);
    padding: 0;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.warning-content {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-lg);
}

.warning-icon {
    font-size: 20px;
}

.warning-text {
    flex: 1;
    color: #92400e;
    font-weight: 500;
    font-size: 14px;
}

.start-new-session-btn {
    background: #f59e0b;
    color: white;
    border: none;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 13px;
}

.start-new-session-btn:hover {
    background: #d97706;
    transform: translateY(-1px);
}

.dismiss-warning-btn {
    background: rgba(146, 64, 14, 0.1);
    border: none;
    color: #92400e;
    font-size: 18px;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dismiss-warning-btn:hover {
    background: rgba(146, 64, 14, 0.2);
    transform: scale(1.05);
}





.new-chat-btn {
    background: var(--primary-100);
    color: var(--text-100);
    border: none;
    padding: 10px 16px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.new-chat-btn:hover {
    background: var(--accent-200);
    box-shadow: var(--shadow-md);
}


.chat-session {
    padding: 12px 16px;
    margin-bottom: 8px;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-session:hover {
    background: var(--surface-light);
    border-color: var(--border-light);
}

.chat-session.active {
    background: var(--primary-300);
    border-color: var(--primary-100);
    box-shadow: var(--shadow-sm);
}

.chat-session.active .chat-session-title {
    color: var(--bg-100);
}

.chat-session.active .chat-session-preview {
    color: var(--bg-200);
}

.chat-session-info {
    flex: 1;
    min-width: 0;
}

.chat-session-title {
    font-weight: 500;
    color: var(--text-100);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    line-height: 1.4;
}

.chat-session-preview {
    font-size: 12px;
    color: var(--text-200);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
}

.chat-session-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chat-session:hover .chat-session-actions {
    opacity: 1;
}

.delete-session-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.75rem;
}

.delete-session-btn:hover {
    background: #c82333;
}

.model-select {
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--border-radius-md);
    background: var(--surface);
    min-width: 180px;
    font-size: 14px;
    color: var(--text-100);
    transition: var(--transition-fast);
}

.model-select:focus {
    outline: none;
    border-color: var(--primary-100);
    box-shadow: 0 0 0 3px rgba(46, 139, 87, 0.2);
}

.model-select option {
    background: var(--bg-200);
    color: var(--text-100);
}

.mcp-btn {
    padding: 8px 16px;
    background: var(--surface);
    color: var(--text-100);
    border: 1px solid var(--border);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: var(--transition-fast);
}

.mcp-btn:hover {
    background: var(--surface-light);
    border-color: var(--border-light);
}



/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    overflow: hidden;
    transition: margin-left var(--transition-normal);
}



.messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-xl);
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    max-width: none;
}

.message {
    padding: var(--space-lg) var(--space-xl);
    border-radius: var(--radius-xl);
    max-width: 75%;
    position: relative;
    box-shadow: var(--shadow-card);
    transition: var(--transition-normal);
    border: 1px solid var(--border-primary);
    font-family: var(--font-primary);
    font-weight: 400;
    letter-spacing: 0.025em;
}

.message:hover .message-actions {
    opacity: 1;
}

/* Message Actions */
.message-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
    justify-content: flex-end;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message-action-btn {
    background: #f1f3f4;
    border: 1px solid #dadce0;
    border-radius: 16px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 12px;
    color: #5f6368;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.message-action-btn:hover {
    background: #e8eaed;
    border-color: #c4c7c5;
    color: #202124;
}

.message.user .message-actions {
    justify-content: flex-start;
}

.message.assistant .message-actions {
    justify-content: flex-end;
}

/* Edit mode styling */
.message.editing .message-content {
    display: none;
}

.message-edit-form {
    display: none;
}

.message.editing .message-edit-form {
    display: block;
}

.message-edit-textarea {
    width: 100%;
    min-height: 60px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: inherit;
    resize: vertical;
}

.message-edit-actions {
    margin-top: 8px;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.message-edit-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.message-edit-btn.save {
    background: #28a745;
    color: white;
}

.message-edit-btn.save:hover {
    background: #218838;
}

.message-edit-btn.cancel {
    background: #6c757d;
    color: white;
}

.message-edit-btn.cancel:hover {
    background: #5a6268;
}

.message.user {
    background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
    color: white;
    align-self: flex-end;
    border-color: var(--primary-400);
    box-shadow: var(--shadow-primary);
}

.message.assistant {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
    align-self: flex-start;
    color: var(--text-primary);
}

.message:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
}

.message.error {
    background: #dc3545;
    color: white;
    align-self: center;
}

.message.streaming {
    background: #f0f0f0;
    color: #666;
    align-self: flex-start;
}

.message.thinking {
    background: linear-gradient(135deg, var(--primary-200), var(--primary-300));
    border: 1px solid var(--primary-400);
    color: var(--bg-primary);
    align-self: flex-start;
    margin-bottom: var(--space-md);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 13px;
    box-shadow: var(--shadow-primary);
}

.thinking-header {
    font-weight: 600;
    padding-bottom: var(--space-sm);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: var(--space-sm);
    user-select: none;
    color: var(--bg-primary);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 14px;
}

.thinking-content {
    white-space: pre-wrap;
    line-height: 1.5;
    max-height: 250px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.1);
    padding: var(--space-md);
    border-radius: var(--radius-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--bg-primary);
}

.typing-indicator {
    animation: typing 1.5s infinite;
    font-size: 1.2rem;
}

@keyframes typing {

    0%,
    20% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

.message.info {
    background: #17a2b8;
    color: white;
    align-self: center;
    font-size: 0.9rem;
}

/* Markdown Styling for Messages */
.message h1,
.message h2,
.message h3,
.message h4,
.message h5,
.message h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    line-height: 1.25;
}

.message h1 {
    font-size: 1.5em;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.message h2 {
    font-size: 1.3em;
    border-bottom: 1px solid #eee;
    padding-bottom: 6px;
}

.message h3 {
    font-size: 1.2em;
}

.message h4 {
    font-size: 1.1em;
}

.message h5 {
    font-size: 1em;
}

.message h6 {
    font-size: 0.9em;
    color: #666;
}

.message p {
    margin: 8px 0;
    line-height: 1.5;
}

.message ul,
.message ol {
    margin: 8px 0;
    padding-left: 20px;
}

.message li {
    margin: 4px 0;
}

.message strong {
    font-weight: 600;
}

.message em {
    font-style: italic;
}

.message code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: var(--font-primary);
    font-size: 0.9em;
}

.message.user code {
    background: rgba(255, 255, 255, 0.2);
}

.message pre {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;
    font-family: var(--font-primary);
    font-size: 0.9em;
    line-height: 1.4;
}

.message.user pre {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.message pre code {
    background: none;
    padding: 0;
    border-radius: 0;
}

.message blockquote {
    border-left: 4px solid rgba(0, 0, 0, 0.2);
    margin: 12px 0;
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.05);
    font-style: italic;
}

.message.user blockquote {
    border-left-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
}

.message table {
    border-collapse: collapse;
    margin: 12px 0;
    width: 100%;
    font-size: 0.9em;
}

.message th,
.message td {
    border: 1px solid rgba(0, 0, 0, 0.2);
    padding: 8px 12px;
    text-align: left;
}

.message th {
    background: rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

.message.user th,
.message.user td {
    border-color: rgba(255, 255, 255, 0.3);
}

.message.user th {
    background: rgba(255, 255, 255, 0.2);
}

.message a {
    color: inherit;
    text-decoration: underline;
    opacity: 0.9;
}

.message a:hover {
    opacity: 1;
}

.message hr {
    border: none;
    border-top: 1px solid rgba(0, 0, 0, 0.2);
    margin: 16px 0;
}

.message.user hr {
    border-top-color: rgba(255, 255, 255, 0.3);
}

/* Input Area */
.input-area {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
    padding: var(--space-xl);
    flex-shrink: 0;
}

.input-container {
    max-width: 100%;
    margin: 0;
}

.input-row {
    display: flex;
    gap: var(--space-md);
    align-items: flex-end;
}

.input-buttons {
    display: flex;
    gap: var(--space-sm);
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    padding: var(--space-md) var(--space-lg);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background: var(--bg-card);
    resize: none;
    min-height: 44px;
    max-height: 120px;
    font-family: var(--font-primary);
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
    transition: var(--transition-fast);
    letter-spacing: 0.025em;
}

#messageInput:focus {
    outline: none;
    border-color: var(--border-accent);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

#messageInput::placeholder {
    color: var(--text-muted);
}



.attach-btn {
    padding: var(--space-md);
    background: var(--bg-card);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-weight: 500;
    height: 44px;
    min-width: 44px;
    font-size: 16px;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.attach-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.attach-btn:disabled {
    background: var(--bg-card);
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.5;
}

.image-preview {
    margin-top: var(--space-md);
    padding: var(--space-lg);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.image-preview img {
    max-width: 120px;
    max-height: 120px;
    border-radius: var(--radius-md);
    object-fit: cover;
}

.image-preview .remove-image {
    padding: var(--space-xs) var(--space-sm);
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: var(--transition-fast);
}

.image-preview .remove-image:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.send-btn {
    padding: var(--space-md) var(--space-xl);
    background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-family: var(--font-primary);
    font-weight: 600;
    height: 44px;
    font-size: 14px;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-primary);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    letter-spacing: 0.025em;
}

.send-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
}

.send-btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.modal-content {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-card-hover);
    border-radius: var(--radius-2xl);
    width: 95%;
    max-width: 900px;
    max-height: 95vh;
    overflow: hidden;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: var(--space-xl);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-card);
}

.modal-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    width: 36px;
    height: 36px;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.close-btn:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
    transform: scale(1.05);
}

.modal-body {
    padding: var(--space-2xl);
    max-height: 70vh;
    overflow-y: auto;
    background: var(--bg-card);
}

/* MCP Section Organization */
.api-key-section,
.enabled-servers-section,
.add-server-section {
    margin-bottom: var(--space-2xl);
    padding: var(--space-2xl);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    background: var(--bg-card);
    box-shadow: var(--shadow-card);
    transition: var(--transition-fast);
}

.api-key-section:hover,
.enabled-servers-section:hover,
.add-server-section:hover {
    border-color: var(--border-secondary);
    box-shadow: var(--shadow-card-hover);
}

.api-key-section {
    border-left: 4px solid var(--primary-400);
}

.enabled-servers-section {
    border-left: 4px solid var(--success-400);
}

.add-server-section {
    border-left: 4px solid var(--warning-400);
}

.api-key-section h3,
.enabled-servers-section h3,
.add-server-section h3 {
    margin-top: 0;
    margin-bottom: var(--space-xl);
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

/* Empty state for enabled servers */
.mcp-servers:empty::before {
    content: "No MCP servers enabled yet. Add servers using the sections below.";
    display: block;
    padding: var(--space-xl);
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 1px dashed var(--border-primary);
}

/* Section spacing improvements */
.api-key-section:last-child,
.enabled-servers-section:last-child,
.add-server-section:last-child {
    margin-bottom: 0;
}



.add-server-form,
.api-key-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.server-input {
    width: 100%;
    padding: var(--space-lg);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    font-size: 14px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-family: inherit;
    transition: var(--transition-fast);
}

.server-input:focus {
    outline: none;
    border-color: var(--border-accent);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    background: var(--bg-card);
}

.server-input::placeholder {
    color: var(--text-secondary);
}

.add-server-btn {
    padding: var(--space-lg) var(--space-xl);
    background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-primary);
}

.add-server-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
}

.add-server-btn:disabled {
    background: var(--bg-disabled);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.server-help,
.api-key-help {
    margin-top: var(--space-lg);
    padding: var(--space-xl);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-400);
}

.server-help p,
.api-key-help p {
    margin: 0 0 var(--space-md) 0;
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
}

.server-help ol,
.api-key-help ol {
    margin: var(--space-md) 0 0 var(--space-lg);
    font-size: 14px;
    color: var(--text-secondary);
}

.server-help li,
.api-key-help li {
    margin-bottom: var(--space-sm);
    line-height: 1.5;
}

.server-help a,
.api-key-help a {
    color: var(--primary-400);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.server-help a:hover,
.api-key-help a:hover {
    color: var(--primary-500);
    text-decoration: underline;
}

/* Registry Section */
.registry-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.registry-results {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

.registry-loading {
    padding: 2rem;
    text-align: center;
    color: #666;
}

.registry-server {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.registry-server:hover {
    background-color: #f8f9fa;
}

.registry-server:last-child {
    border-bottom: none;
}

.registry-server-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.registry-server-name {
    font-weight: 600;
    color: #333;
    margin: 0;
}

.registry-server-author {
    font-size: 0.9rem;
    color: #666;
    margin-left: 0.5rem;
}

.registry-server-description {
    color: #555;
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.registry-server-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #777;
    margin: 0.5rem 0;
}

.registry-server-tools {
    font-size: 0.8rem;
    color: #666;
    margin: 0.5rem 0;
}

.registry-server-actions {
    margin-top: 0.5rem;
}

.add-registry-server-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.add-registry-server-btn:hover {
    background: #218838;
}

.registry-empty {
    padding: 2rem;
    text-align: center;
    color: #666;
}

/* MCP Servers */
.mcp-servers {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mcp-server {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
}

.mcp-server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.mcp-server-name {
    font-weight: 600;
    color: #333;
}

.mcp-server-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.mcp-server-status.connected {
    background: #d4edda;
    color: #155724;
}

.mcp-server-status.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.mcp-server-description {
    color: #666;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.mcp-server-tools {
    font-size: 0.875rem;
    color: #666;
}

.mcp-server-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

.connect-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.connect-btn.connect {
    background: #28a745;
    color: white;
}

.connect-btn.disconnect {
    background: #dc3545;
    color: white;
}

.connect-btn:hover {
    opacity: 0.9;
}

.connect-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.delete-btn {
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.85rem;
}

.delete-btn:hover {
    background: #c82333;
}

.delete-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Chat History */
.history-controls {
    margin-bottom: 1rem;
    text-align: right;
}

.clear-history-btn {
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.clear-history-btn:hover {
    background: #c82333;
}

.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0.5rem;
}

.history-item:last-child {
    border-bottom: none;
}

.history-item.user {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
}

.history-item.assistant {
    background: #fff;
    border-left: 4px solid #28a745;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.history-role {
    font-weight: 600;
    text-transform: capitalize;
}

.history-timestamp {
    font-size: 0.8rem;
    color: #666;
}

.history-content {
    white-space: pre-wrap;
    line-height: 1.4;
}

.history-meta {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #666;
}

/* Loading */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 1rem;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .message {
        max-width: 95%;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}