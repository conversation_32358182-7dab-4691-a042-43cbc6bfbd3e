{"d433777b-c008-44ee-b945-d6c3013d3dd5": {"title": "Chat d433777b", "created_at": "2025-07-17T12:56:11.513676", "messages": [{"role": "user", "content": "hi", "timestamp": "2025-07-17T12:56:27.028476", "has_image": false, "model": "magistral:latest", "tokens": 1}, {"role": "assistant", "content": "Hello! How can I assist you today?", "content_html": "<p>Hello! How can I assist you today?</p>", "timestamp": "2025-07-17T12:56:50.917313", "model": "magistral:latest", "tools_used": [], "tokens": 10, "context_status": {"current_tokens": 10, "context_window": 8192, "usage_percentage": 0.1220703125, "tokens_remaining": 8182, "should_start_new_session": false, "model_name": "magistral:latest", "model_contexts": {"magistral:latest": {"tokens": 10, "context_window": 8192}}}}]}, "73e68af7-2f3b-4ae3-b6da-42246fb814d0": {"title": "Chat 73e68af7", "created_at": "2025-07-17T13:00:38.840477", "messages": [{"role": "user", "content": "what is capital of india", "timestamp": "2025-07-17T13:00:59.186178", "has_image": false, "model": "magistral:latest", "tokens": 5}, {"role": "assistant", "content": "The capital of India is New Delhi.\n\nWould you like more details about it?", "content_html": "<p>The capital of India is New Delhi.</p>\n<p>Would you like more details about it?</p>", "timestamp": "2025-07-17T13:01:23.704894", "model": "magistral:latest", "tools_used": [], "tokens": 21, "context_status": {"current_tokens": 21, "context_window": 8192, "usage_percentage": 0.25634765625, "tokens_remaining": 8171, "should_start_new_session": false, "model_name": "magistral:latest", "model_contexts": {"magistral:latest": {"tokens": 31, "context_window": 8192}}}}, {"role": "user", "content": "hi", "timestamp": "2025-07-17T13:21:47.966369", "has_image": false, "model": "magistral:latest", "tokens": 22}, {"role": "assistant", "content": "Hello! How can I assist you today?", "content_html": "<p>Hello! How can I assist you today?</p>", "timestamp": "2025-07-17T13:22:16.474434", "model": "magistral:latest", "tools_used": [], "tokens": 31, "context_status": {"current_tokens": 31, "context_window": 8192, "usage_percentage": 0.37841796875, "tokens_remaining": 8161, "should_start_new_session": false, "model_name": "magistral:latest", "model_contexts": {"magistral:latest": {"tokens": 31, "context_window": 8192}}}}]}, "e7f01b5c-028c-4bd1-acd6-92af50a5829f": {"title": "Chat e7f01b5c", "created_at": "2025-07-17T14:18:33.321216", "messages": [{"role": "user", "content": "Remember this: my favorite color is blue", "timestamp": "2025-07-17T14:19:01.403897", "has_image": false, "model": "devstral:latest", "tokens": 8}, {"role": "assistant", "content": "I have remembered that your favorite color is blue. If you need anything else or want to discuss something, feel free to let me know!", "content_html": "<p>I have remembered that your favorite color is blue. If you need anything else or want to discuss something, feel free to let me know!</p>", "timestamp": "2025-07-17T14:20:01.485557", "model": "devstral:latest", "tools_used": [], "tokens": 36, "context_status": {"current_tokens": 36, "context_window": 8192, "usage_percentage": 0.439453125, "tokens_remaining": 8156, "should_start_new_session": false, "model_name": "devstral:latest", "model_contexts": {"devstral:latest": {"tokens": 36, "context_window": 8192}}}}]}}