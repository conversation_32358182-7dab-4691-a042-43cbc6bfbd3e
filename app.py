"""
Ollama Chat Backend Server with MCP Integration
Flask backend that connects to Ollama and MCP servers for enhanced functionality
"""

from flask import Flask, render_template, request, jsonify, Response
import requests
import json
import logging
import base64
import asyncio
import aiohttp
from datetime import datetime
import threading
import time
import os
import uuid
import tiktoken
import markdown
from werkzeug.utils import secure_filename
from PIL import Image
import io
import base64
import aiohttp
import ssl
from dotenv import load_dotenv
from langchain.memory import ConversationBufferWindowMemory
from langchain.schema import HumanMessage, AIMessage

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChatMemoryManager:
    """Manages chat memory with token counting and context window management"""

    def __init__(self):
        self.sessions = {}
        self.encoding = tiktoken.get_encoding("cl100k_base")  # GPT-4 encoding

    def get_or_create_session(self, session_id, model_name=None):
        """Get or create a chat session with memory"""
        if session_id not in self.sessions:
            # Get model context window
            context_window = self.get_model_context_window(model_name)

            self.sessions[session_id] = {
                'memory': ConversationBufferWindowMemory(
                    k=50,  # Keep last 50 messages as fallback
                    return_messages=True
                ),
                'context_window': context_window,
                'current_tokens': 0,
                'model_name': model_name,
                'created_at': datetime.now().isoformat(),
                'messages': [],
                'model_contexts': {}  # Track context per model within session
            }

        # Update model context if model changed
        if model_name and model_name != self.sessions[session_id].get('model_name'):
            self.sessions[session_id]['model_name'] = model_name
            # Update context window for new model
            new_context_window = self.get_model_context_window(model_name)
            self.sessions[session_id]['context_window'] = new_context_window

        return self.sessions[session_id]

    def get_model_context_window(self, model_name):
        """Get context window size for different models"""
        if not model_name:
            return 4096  # Default fallback

        # Common Ollama model context windows
        context_windows = {
            'llama2': 4096,
            'llama3': 8192,
            'llama3.1': 128000,
            'llama3.2': 128000,
            'mistral': 8192,
            'mistral-small': 32768,
            'mistral-medium': 32768,
            'mistral-large': 32768,
            'codellama': 16384,
            'vicuna': 4096,
            'orca': 4096,
            'phi': 2048,
            'gemma': 8192,
            'qwen': 32768,
            'deepseek': 16384,
            'solar': 4096
        }

        # Try to match model name with known patterns
        model_lower = model_name.lower()
        for key, window in context_windows.items():
            if key in model_lower:
                return window

        # Try to get from Ollama API
        try:
            response = requests.get(f"{OLLAMA_BASE_URL}/api/show",
                                  json={"name": model_name},
                                  timeout=5)
            if response.status_code == 200:
                model_info = response.json()
                # Look for context length in model parameters
                if 'parameters' in model_info:
                    params = model_info['parameters']
                    if 'num_ctx' in params:
                        return int(params['num_ctx'])
        except Exception as e:
            logger.warning(f"Could not get context window for {model_name}: {e}")

        return 8192  # Default for most modern models

    def model_supports_tools(self, model_name):
        """Check if a model supports tool calling by querying Ollama API"""
        if not model_name:
            return False

        try:
            # Query Ollama API to get model capabilities
            response = requests.post(f"{OLLAMA_BASE_URL}/api/show",
                                   json={"name": model_name},
                                   timeout=5)
            if response.status_code == 200:
                model_info = response.json()
                capabilities = model_info.get('capabilities', [])

                # Check if 'tools' is in the capabilities list
                supports_tools = 'tools' in capabilities
                logger.info(f"Model {model_name} capabilities: {capabilities}, supports tools: {supports_tools}")
                return supports_tools
            else:
                logger.warning(f"Failed to get model info for {model_name}: {response.status_code}")
                return False
        except Exception as e:
            logger.warning(f"Could not check tool support for {model_name}: {e}")
            return False

    def count_tokens(self, text):
        """Count tokens in text"""
        try:
            return len(self.encoding.encode(text))
        except Exception:
            # Fallback: rough estimation (1 token ≈ 4 characters)
            return len(text) // 4

    def add_message(self, session_id, role, content, model_name=None):
        """Add a message to session memory with token tracking per model"""
        session = self.get_or_create_session(session_id, model_name)

        # Count tokens in the new message
        message_tokens = self.count_tokens(content)

        # Create message object
        message = {
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            'tokens': message_tokens,
            'model': model_name
        }

        # Add to session messages
        session['messages'].append(message)

        # Track tokens per model within session
        if model_name:
            if model_name not in session['model_contexts']:
                session['model_contexts'][model_name] = {
                    'tokens': 0,
                    'context_window': self.get_model_context_window(model_name)
                }
            session['model_contexts'][model_name]['tokens'] += message_tokens

        # Update current tokens for the active model
        session['current_tokens'] += message_tokens

        # Add to LangChain memory
        if role == 'user':
            session['memory'].chat_memory.add_user_message(content)
        else:
            session['memory'].chat_memory.add_ai_message(content)

        # Check if we need to trim context for current model
        self.manage_context_window(session_id, model_name)

        return message

    def manage_context_window(self, session_id, model_name=None):
        """Manage context window by removing old messages if needed, per model"""
        session = self.sessions[session_id]

        # Get context limit for current model
        if model_name and model_name in session['model_contexts']:
            context_window = session['model_contexts'][model_name]['context_window']
            current_tokens = session['model_contexts'][model_name]['tokens']
        else:
            context_window = session['context_window']
            current_tokens = session['current_tokens']

        context_limit = int(context_window * 0.8)  # Use 80% of context window

        while current_tokens > context_limit and len(session['messages']) > 2:
            # Remove oldest message (but keep at least 2 messages)
            removed_message = session['messages'].pop(0)
            removed_tokens = removed_message['tokens']
            removed_model = removed_message.get('model')

            # Update token counts
            session['current_tokens'] -= removed_tokens
            if removed_model and removed_model in session['model_contexts']:
                session['model_contexts'][removed_model]['tokens'] -= removed_tokens

            # Update current tokens for tracking
            if model_name and model_name in session['model_contexts']:
                current_tokens = session['model_contexts'][model_name]['tokens']
            else:
                current_tokens = session['current_tokens']

            # Also remove from LangChain memory
            if session['memory'].chat_memory.messages:
                session['memory'].chat_memory.messages.pop(0)

    def get_context_status(self, session_id, model_name=None):
        """Get context window status for a session and specific model"""
        if session_id not in self.sessions:
            return None

        session = self.sessions[session_id]

        # Get context info for specific model if provided
        if model_name and model_name in session['model_contexts']:
            context_window = session['model_contexts'][model_name]['context_window']
            current_tokens = session['model_contexts'][model_name]['tokens']
        else:
            # Fall back to session-level context
            context_window = session['context_window']
            current_tokens = session['current_tokens']

        usage_percentage = (current_tokens / context_window) * 100
        tokens_remaining = context_window - current_tokens
        should_start_new_session = usage_percentage > 85  # Suggest new session at 85%

        return {
            'current_tokens': current_tokens,
            'context_window': context_window,
            'usage_percentage': usage_percentage,
            'tokens_remaining': tokens_remaining,
            'should_start_new_session': should_start_new_session,
            'model_name': model_name,
            'model_contexts': session.get('model_contexts', {})
        }

    def get_conversation_history(self, session_id, include_system_prompt=True):
        """Get formatted conversation history for the model"""
        if session_id not in self.sessions:
            return []

        session = self.sessions[session_id]
        messages = []

        # Add system prompt if requested
        if include_system_prompt:
            messages.append({
                'role': 'system',
                'content': 'You are a helpful AI assistant with access to various tools. Use tools when needed to provide accurate and up-to-date information.'
            })

        # Add conversation history
        for msg in session['messages']:
            messages.append({
                'role': msg['role'],
                'content': msg['content']
            })

        return messages

    def clear_session(self, session_id):
        """Clear a chat session"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'

# Ensure upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Configuration
OLLAMA_BASE_URL = os.getenv("OLLAMA_URL", "http://localhost:11434")
SMITHERY_API_KEY = os.getenv("SMITHERY_API_KEY", "")
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp'}

class MCPManager:
    """Manager for MCP server connections using HTTP API"""

    def __init__(self):
        self.connections = {}
        self.smithery_api_key = SMITHERY_API_KEY  # Load from environment
        self.servers_file = 'mcp_servers.json'
        self.api_key_file = 'smithery_api_key.txt'

        # Start with empty servers - users will add their own
        self.available_servers = {}

        # Load saved servers and API key on startup
        self.load_servers()
        self.load_api_key()

        # Auto-reconnect to previously connected servers
        self.auto_reconnect_servers()

    def load_servers(self):
        """Load saved MCP servers from file"""
        try:
            if os.path.exists(self.servers_file):
                with open(self.servers_file, 'r') as f:
                    saved_servers = json.load(f)
                    self.available_servers.update(saved_servers)
                    logger.info(f"Loaded {len(saved_servers)} saved MCP servers")
        except Exception as e:
            logger.error(f"Error loading servers: {e}")

    def save_servers(self):
        """Save MCP servers to file"""
        try:
            with open(self.servers_file, 'w') as f:
                json.dump(self.available_servers, f, indent=2)
                logger.info("Saved MCP servers to file")
        except Exception as e:
            logger.error(f"Error saving servers: {e}")

    def load_api_key(self):
        """Load saved Smithery API key from file"""
        try:
            if os.path.exists(self.api_key_file):
                with open(self.api_key_file, 'r') as f:
                    self.smithery_api_key = f.read().strip()
                    if self.smithery_api_key:
                        logger.info("Loaded saved Smithery API key")
        except Exception as e:
            logger.error(f"Error loading API key: {e}")

    def save_api_key(self):
        """Save Smithery API key to file"""
        try:
            with open(self.api_key_file, 'w') as f:
                f.write(self.smithery_api_key or '')
                logger.info("Saved Smithery API key to file")
        except Exception as e:
            logger.error(f"Error saving API key: {e}")

    def auto_reconnect_servers(self):
        """Auto-reconnect to previously connected servers"""
        if not self.smithery_api_key:
            logger.info("No API key available for auto-reconnect")
            return

        for server_id, server_config in self.available_servers.items():
            if server_config.get('custom', False):
                logger.info(f"Auto-reconnecting to {server_id}...")
                # Use asyncio to run the async connect method
                try:
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result = loop.run_until_complete(self.connect_to_server(server_id))
                    loop.close()

                    if result.get('success'):
                        logger.info(f"Successfully reconnected to {server_id}")
                    else:
                        logger.warning(f"Failed to reconnect to {server_id}: {result.get('error', 'Unknown error')}")
                except Exception as e:
                    logger.error(f"Error reconnecting to {server_id}: {e}")

    def set_api_key(self, api_key):
        """Set the Smithery API key for authentication"""
        self.smithery_api_key = api_key
        self.save_api_key()
        logger.info("Smithery API key updated")

    async def fetch_smithery_registry(self):
        """Fetch complete list of available MCP servers from Smithery registry"""
        if not self.smithery_api_key:
            return {
                'success': False,
                'error': 'No Smithery API key configured'
            }

        try:
            registry_url = "https://registry.smithery.ai/servers"
            headers = {
                'Authorization': f'Bearer {self.smithery_api_key}',
                'Accept': 'application/json',
                'User-Agent': 'ollama-chat-app/1.0.0'
            }

            logger.info("Fetching complete Smithery registry...")

            async with aiohttp.ClientSession() as session:
                async with session.get(registry_url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        servers = []

                        logger.info(f"Registry API response keys: {list(data.keys())}")
                        logger.info(f"Registry API response sample: {str(data)[:500]}...")

                        # Handle different response formats
                        server_list = data.get('servers', data.get('data', []))
                        if isinstance(server_list, list):
                            for server in server_list:
                                qualified_name = server.get('qualifiedName', server.get('name', ''))
                                display_name = server.get('displayName', qualified_name)

                                # Extract author from qualified name (e.g., "@upstash/context7-mcp" -> "upstash")
                                author = 'Unknown'
                                if qualified_name.startswith('@') and '/' in qualified_name:
                                    author = qualified_name.split('/')[0][1:]  # Remove @ and get first part

                                server_info = {
                                    'id': qualified_name.replace('/', '_').replace('@', '_'),
                                    'name': display_name,
                                    'qualified_name': qualified_name,
                                    'description': server.get('description', ''),
                                    'url': f"https://server.smithery.ai/{qualified_name}/mcp" if qualified_name else "",
                                    'author': author,
                                    'version': server.get('version', '1.0.0'),
                                    'tags': server.get('tags', []),
                                    'tools_count': len(server.get('tools', [])),
                                    'tools': [tool.get('name', '') for tool in server.get('tools', [])],
                                    'use_count': server.get('useCount', 0),
                                    'homepage': server.get('homepage', '')
                                }
                                # Only add servers with valid names
                                if qualified_name:
                                    servers.append(server_info)

                        logger.info(f"Fetched {len(servers)} servers from Smithery registry")
                        return {
                            'success': True,
                            'servers': servers
                        }
                    else:
                        response_text = await response.text()
                        logger.error(f"Registry API error: {response.status} - {response_text}")
                        return {
                            'success': False,
                            'error': f"Registry API error: {response.status}"
                        }

        except Exception as e:
            logger.error(f"Error fetching Smithery registry: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def fetch_tools_from_registry(self, server_name, server_url):
        """Fetch tools with schemas from Smithery Registry API"""
        try:
            # Extract qualified name from server URL
            # From: https://server.smithery.ai/@owner/repo/mcp or https://smithery.ai/server/@owner/repo
            if 'server.smithery.ai' in server_url:
                # Extract from server URL: https://server.smithery.ai/@owner/repo/mcp
                qualified_name = server_url.replace('https://server.smithery.ai/', '').replace('/mcp', '')
            elif 'smithery.ai/server' in server_url:
                # Extract from registry URL: https://smithery.ai/server/@owner/repo
                qualified_name = server_url.replace('https://smithery.ai/server/', '')
            else:
                return None

            # Remove query parameters but keep @ prefix for registry lookup
            if '?' in qualified_name:
                qualified_name = qualified_name.split('?')[0]

            # For registry API, we need to URL encode the qualified name
            import urllib.parse
            encoded_name = urllib.parse.quote(qualified_name, safe='')
            registry_url = f"https://registry.smithery.ai/servers/{encoded_name}"
            headers = {
                'Authorization': f'Bearer {self.smithery_api_key}',
                'Accept': 'application/json',
                'User-Agent': 'ollama-chat-app/1.0.0'
            }

            logger.info(f"Fetching tools from registry: {registry_url}")

            async with aiohttp.ClientSession() as session:
                async with session.get(registry_url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        tools_with_schemas = []

                        if 'tools' in data and data['tools']:
                            for tool in data['tools']:
                                tool_info = {
                                    'name': tool['name'],
                                    'description': tool.get('description', f"Tool: {tool['name']}"),
                                    'schema': tool.get('inputSchema', {})
                                }
                                tools_with_schemas.append(tool_info)

                            logger.info(f"Fetched {len(tools_with_schemas)} tools with schemas from registry for {qualified_name}")
                            return tools_with_schemas
                        else:
                            logger.warning(f"No tools found in registry for {qualified_name}")
                            return []
                    else:
                        response_text = await response.text()
                        logger.error(f"Registry API error: {response.status} - {response_text}")
                        return None

        except Exception as e:
            logger.error(f"Error fetching tools from registry: {e}")
            return None
    
    async def connect_to_server(self, server_name):
        """Connect to an MCP server using HTTP"""
        if server_name not in self.available_servers:
            raise ValueError(f"Unknown server: {server_name}")

        server_config = self.available_servers[server_name]
        base_url = server_config['url']

        # For Smithery servers, construct the correct MCP endpoint
        if 'server.smithery.ai' in base_url:
            # URL is already in correct format: https://server.smithery.ai/@owner/repo/mcp
            url = base_url
        elif 'smithery.ai' in base_url:
            # Convert registry URL to server URL
            # From: https://smithery.ai/server/@owner/repo
            # To: https://server.smithery.ai/@owner/repo/mcp
            url = base_url.replace('smithery.ai/server/', 'server.smithery.ai/') + '/mcp'
        else:
            # Standard MCP endpoint
            url = base_url + '/mcp' if not base_url.endswith('/') else base_url + 'mcp'

        try:
            # Initialize MCP connection via HTTP (Streamable HTTP transport)
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream',
                'User-Agent': 'ollama-chat-app/1.0.0',
                'Cache-Control': 'no-cache'
            }

            # Add Smithery authentication if available and needed
            if 'smithery.ai' in base_url and self.smithery_api_key:
                headers['Authorization'] = f'Bearer {self.smithery_api_key}'

            async with aiohttp.ClientSession() as session:
                # Send initialize request
                init_payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {}
                        },
                        "clientInfo": {
                            "name": "ollama-chat-app",
                            "version": "1.0.0"
                        }
                    }
                }

                async with session.post(url, json=init_payload, headers=headers) as response:
                    if response.status == 200:
                        # Handle both JSON and SSE responses
                        content_type = response.headers.get('content-type', '')
                        if 'text/event-stream' in content_type:
                            # Handle Server-Sent Events - Smithery servers use SSE
                            response_text = await response.text()
                            logger.info(f"SSE response from {server_name}: {response_text[:200]}...")
                            # For SSE mode, we'll skip direct connection and use registry fallback
                            raise Exception("SSE mode - using registry fallback")
                        else:
                            init_result = await response.json()
                            logger.info(f"MCP Initialize response: {init_result}")

                        # List available tools
                        tools_payload = {
                            "jsonrpc": "2.0",
                            "id": 2,
                            "method": "tools/list",
                            "params": {}
                        }

                        async with session.post(url, json=tools_payload, headers=headers) as tools_response:
                            if tools_response.status == 200:
                                tools_result = await tools_response.json()
                                tools = []
                                if 'result' in tools_result and 'tools' in tools_result['result']:
                                    tools = [tool['name'] for tool in tools_result['result']['tools']]

                                # Mark as connected and store session info
                                self.available_servers[server_name]['connected'] = True
                                self.available_servers[server_name]['tools'] = tools
                                self.connections[server_name] = {
                                    'url': url,
                                    'tools': tools,
                                    'connected_at': datetime.now().isoformat(),
                                    'session_initialized': True,
                                    'capabilities': init_result.get('result', {}).get('capabilities', {})
                                }

                                logger.info(f"Connected to {server_name} with tools: {tools}")
                                return {
                                    'success': True,
                                    'tools': tools
                                }
                            else:
                                raise Exception(f"Failed to list tools: {tools_response.status}")
                    else:
                        raise Exception(f"Failed to initialize: {response.status}")

        except Exception as e:
            logger.error(f"Failed to connect to {server_name}: {e}")

            # Try to fetch tools from Smithery Registry API if it's a Smithery server
            if 'smithery.ai' in base_url and self.smithery_api_key:
                try:
                    tools_with_schemas = await self.fetch_tools_from_registry(server_name, base_url)
                    if tools_with_schemas:
                        # Extract tool names for backward compatibility
                        tool_names = [tool['name'] for tool in tools_with_schemas]

                        self.available_servers[server_name]['connected'] = True
                        self.available_servers[server_name]['tools'] = tool_names
                        self.available_servers[server_name]['tool_schemas'] = tools_with_schemas
                        self.connections[server_name] = {
                            'url': url,
                            'tools': tool_names,
                            'tool_schemas': tools_with_schemas,
                            'connected_at': datetime.now().isoformat(),
                            'registry_mode': True
                        }

                        logger.info(f"Fetched tools from registry for {server_name}: {tool_names}")
                        return {
                            'success': True,
                            'tools': tool_names,
                            'tool_schemas': tools_with_schemas,
                            'registry_mode': True
                        }
                except Exception as registry_error:
                    logger.error(f"Failed to fetch from registry: {registry_error}")

            return {
                'success': False,
                'error': str(e)
            }

    async def initialize_mcp_session(self, server_name, url, headers):
        """Initialize MCP session for Smithery servers"""
        try:
            async with aiohttp.ClientSession() as session:
                # Initialize connection
                init_payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {}
                        },
                        "clientInfo": {
                            "name": "ollama-chat-app",
                            "version": "1.0.0"
                        }
                    }
                }

                async with session.post(url, json=init_payload, headers=headers) as response:
                    if response.status == 200:
                        # Handle both JSON and SSE responses
                        content_type = response.headers.get('content-type', '')
                        if 'text/event-stream' in content_type:
                            # Handle Server-Sent Events - Parse the SSE response
                            response_text = await response.text()
                            logger.info(f"SSE session response from {server_name}: {response_text[:200]}...")

                            # Parse SSE to extract initialization result
                            session_result = await self.parse_sse_session_response(response_text)
                            if session_result:
                                self.connections[server_name]['session_initialized'] = True
                                logger.info(f"Session initialized for {server_name} via SSE")
                                return True
                            else:
                                logger.error(f"Failed to parse SSE session response for {server_name}")
                                return False
                        else:
                            init_result = await response.json()
                            self.connections[server_name]['session_initialized'] = True
                            logger.info(f"Session initialized for {server_name}")
                            return True
                    else:
                        logger.error(f"Failed to initialize session for {server_name}: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error initializing session for {server_name}: {e}")
            return False

    async def execute_tool(self, server_name, tool_name, arguments):
        """Execute a tool on an MCP server using HTTP"""
        if server_name not in self.connections:
            await self.connect_to_server(server_name)

        if server_name not in self.connections:
            raise ValueError(f"Could not connect to server: {server_name}")

        connection = self.connections[server_name]
        url = connection['url']

        # For registry mode, we need to use the actual server URL for execution
        if connection.get('registry_mode'):
            # Get the server config to find the actual server URL
            server_config = self.available_servers.get(server_name, {})
            base_url = server_config.get('url', '')
            if 'server.smithery.ai' in base_url:
                url = base_url
            elif 'smithery.ai' in base_url:
                url = base_url.replace('smithery.ai/server/', 'server.smithery.ai/') + '/mcp'

        try:
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream',  # Required by Smithery servers
                'User-Agent': 'ollama-chat-app/1.0.0',
                'Cache-Control': 'no-cache'
            }

            # Add Smithery authentication if available and needed
            if 'smithery.ai' in url and self.smithery_api_key:
                headers['Authorization'] = f'Bearer {self.smithery_api_key}'

            # For Smithery servers, use a different approach
            if 'smithery.ai' in url:
                # Smithery servers work with direct tool execution
                return await self.execute_smithery_tool(url, tool_name, arguments, headers)

            async with aiohttp.ClientSession() as session:
                # Execute tool via MCP for non-Smithery servers
                tool_payload = {
                    "jsonrpc": "2.0",
                    "id": 3,
                    "method": "tools/call",
                    "params": {
                        "name": tool_name,
                        "arguments": arguments
                    }
                }

                async with session.post(url, json=tool_payload, headers=headers) as response:
                    if response.status == 200:
                        # Handle both JSON and SSE responses
                        content_type = response.headers.get('content-type', '')
                        if 'text/event-stream' in content_type:
                            # Handle Server-Sent Events for tool execution
                            response_text = await response.text()
                            logger.info(f"SSE tool response: {response_text[:500]}...")

                            # Parse SSE data to extract JSON responses
                            result = await self.parse_sse_response(response_text)
                            if result:
                                return {
                                    'success': True,
                                    'result': result
                                }
                            else:
                                return {
                                    'success': False,
                                    'error': 'Failed to parse SSE response'
                                }
                        else:
                            result = await response.json()
                            logger.info(f"Tool execution result: {result}")

                            if 'result' in result:
                                return {
                                    'success': True,
                                    'result': result['result'].get('content', [])
                                }
                            elif 'error' in result:
                                return {
                                    'success': False,
                                    'error': result['error'].get('message', 'Unknown error')
                                }
                            else:
                                return {
                                    'success': False,
                                    'error': 'Invalid response format'
                                }
                    else:
                        raise Exception(f"HTTP {response.status}: {await response.text()}")

        except Exception as e:
            logger.error(f"Tool execution failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def parse_sse_response(self, sse_text):
        """Parse Server-Sent Events response to extract JSON data"""
        try:
            lines = sse_text.strip().split('\n')
            result_data = []

            for line in lines:
                line = line.strip()
                if line.startswith('data: '):
                    data_content = line[6:]  # Remove 'data: ' prefix
                    if data_content and data_content != '[DONE]':
                        try:
                            json_data = json.loads(data_content)
                            if 'result' in json_data:
                                # Extract content from MCP result
                                if 'content' in json_data['result']:
                                    result_data.extend(json_data['result']['content'])
                                else:
                                    result_data.append(json_data['result'])
                            elif 'error' in json_data:
                                logger.error(f"SSE error: {json_data['error']}")
                                return None
                        except json.JSONDecodeError:
                            # Skip non-JSON lines
                            continue

            return result_data if result_data else [{'type': 'text', 'text': 'Tool executed successfully'}]

        except Exception as e:
            logger.error(f"Error parsing SSE response: {e}")
            return None

    async def parse_sse_session_response(self, sse_text):
        """Parse Server-Sent Events response for session initialization"""
        try:
            lines = sse_text.strip().split('\n')

            for line in lines:
                line = line.strip()
                if line.startswith('data: '):
                    data_content = line[6:]  # Remove 'data: ' prefix
                    if data_content and data_content != '[DONE]':
                        try:
                            json_data = json.loads(data_content)
                            if 'result' in json_data and 'protocolVersion' in json_data['result']:
                                # Valid MCP initialization response
                                logger.info(f"SSE session initialized: {json_data['result'].get('serverInfo', {}).get('name', 'Unknown')}")
                                return True
                            elif 'error' in json_data:
                                logger.error(f"SSE session error: {json_data['error']}")
                                return False
                        except json.JSONDecodeError:
                            # Skip non-JSON lines
                            continue

            return False

        except Exception as e:
            logger.error(f"Error parsing SSE session response: {e}")
            return False

    async def execute_smithery_tool(self, url, tool_name, arguments, headers):
        """Execute tool on Smithery server with proper session management"""
        try:
            # Use a persistent session to maintain connection
            async with aiohttp.ClientSession() as session:
                # Step 1: Initialize the connection and get session ID
                init_payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {}
                        },
                        "clientInfo": {
                            "name": "ollama-chat-app",
                            "version": "1.0.0"
                        }
                    }
                }

                # Initialize connection and extract session ID
                async with session.post(url, json=init_payload, headers=headers) as init_response:
                    if init_response.status != 200:
                        raise Exception(f"Failed to initialize: {init_response.status}")

                    # Extract session ID from response headers
                    session_id = init_response.headers.get('mcp-session-id')
                    if not session_id:
                        # Try to extract from SSE response
                        if 'text/event-stream' in init_response.headers.get('content-type', ''):
                            init_text = await init_response.text()
                            logger.info(f"Smithery init SSE: {init_text[:200]}...")
                            # Generate a session ID for tracking
                            session_id = str(uuid.uuid4())
                        else:
                            raise Exception("No session ID received from server")

                # Step 2: Send notifications/initialized
                initialized_payload = {
                    "jsonrpc": "2.0",
                    "method": "notifications/initialized"
                }

                # Add session ID to headers for subsequent requests
                session_headers = headers.copy()
                session_headers['mcp-session-id'] = session_id

                async with session.post(url, json=initialized_payload, headers=session_headers) as init_notif_response:
                    if init_notif_response.status != 200:
                        logger.warning(f"Failed to send initialized notification: {init_notif_response.status}")

                # Step 3: Execute the tool with session ID
                tool_payload = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/call",
                    "params": {
                        "name": tool_name,
                        "arguments": arguments
                    }
                }

                async with session.post(url, json=tool_payload, headers=session_headers) as tool_response:
                    if tool_response.status == 200:
                        content_type = tool_response.headers.get('content-type', '')
                        if 'text/event-stream' in content_type:
                            # Handle SSE response
                            response_text = await tool_response.text()
                            logger.info(f"Smithery tool SSE response: {response_text[:500]}...")

                            result = await self.parse_sse_response(response_text)
                            if result:
                                return {
                                    'success': True,
                                    'result': result
                                }
                            else:
                                return {
                                    'success': False,
                                    'error': 'Failed to parse tool response'
                                }
                        else:
                            # Handle JSON response
                            result = await tool_response.json()
                            if 'result' in result:
                                return {
                                    'success': True,
                                    'result': result['result'].get('content', [])
                                }
                            elif 'error' in result:
                                return {
                                    'success': False,
                                    'error': result['error'].get('message', 'Unknown error')
                                }
                    else:
                        response_text = await tool_response.text()
                        raise Exception(f"HTTP {tool_response.status}: {response_text}")

        except Exception as e:
            logger.error(f"Smithery tool execution failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_available_servers(self):
        """Get list of available MCP servers"""
        return {
            name: {
                'description': config['description'],
                'tools': config['tools'],
                'connected': config.get('connected', False),
                'custom': config.get('custom', False)
            }
            for name, config in self.available_servers.items()
        }

    def get_connected_tools(self):
        """Get all tools from connected MCP servers"""
        connected_tools = {}
        for server_name, server_config in self.available_servers.items():
            if server_config.get('connected', False):
                for tool in server_config.get('tools', []):
                    tool_key = f"{server_name}.{tool}"
                    connected_tools[tool_key] = {
                        'server': server_name,
                        'tool': tool,
                        'description': f"Tool '{tool}' from {server_config['description']}"
                    }
        return connected_tools

    def format_tools_for_prompt(self, model_name=None):
        """Format connected tools with schemas for inclusion in Ollama prompt"""
        tools = self.get_connected_tools()
        if not tools:
            return ""

        # Check if the model supports tools
        if model_name and not memory_manager.model_supports_tools(model_name):
            logger.info(f"Model {model_name} does not support tools, skipping tool formatting")
            return ""

        tool_descriptions = []
        tool_descriptions.append("\n=== AVAILABLE TOOLS ===")
        tool_descriptions.append("You have access to the following tools. You MUST use them when the user asks for information that requires web search, weather data, or other external information:")

        # Get detailed tool schemas from connected servers
        for tool_key, tool_info in tools.items():
            server_name = tool_info['server']
            tool_name = tool_info['tool']

            # Get tool schema from server connection
            tool_schema = None
            if server_name in self.connections and 'tool_schemas' in self.connections[server_name]:
                for schema in self.connections[server_name]['tool_schemas']:
                    if schema['name'] == tool_name:
                        tool_schema = schema
                        break

            if tool_schema:
                tool_descriptions.append(f"\n**{tool_key}**: {tool_schema['description']}")

                # Add schema information
                if 'schema' in tool_schema and tool_schema['schema']:
                    schema_info = tool_schema['schema']
                    if 'properties' in schema_info:
                        tool_descriptions.append("  Parameters:")
                        for param, param_info in schema_info['properties'].items():
                            param_type = param_info.get('type', 'string')
                            param_desc = param_info.get('description', '')
                            required = param in schema_info.get('required', [])
                            req_text = " (required)" if required else " (optional)"
                            tool_descriptions.append(f"    - {param} ({param_type}){req_text}: {param_desc}")

                    # Add example
                    example_args = {}
                    if 'properties' in schema_info:
                        for param, param_info in schema_info['properties'].items():
                            if param in schema_info.get('required', []):
                                if 'query' in param.lower():
                                    example_args[param] = "your search query"
                                elif 'location' in param.lower():
                                    example_args[param] = "Munich"
                                else:
                                    example_args[param] = f"example_{param}"

                    if example_args:
                        example_json = json.dumps(example_args)
                        tool_descriptions.append(f"  Example: [TOOL:{tool_name}:{example_json}]")
            else:
                tool_descriptions.append(f"- {tool_key}: {tool_info['description']}")

        tool_descriptions.append("\nIMPORTANT: When you need to search for information, use tools by including this exact format in your response:")
        tool_descriptions.append("[TOOL:tool_name:{\"argument\":\"value\"}]")
        tool_descriptions.append("\nDo NOT say you cannot access external information - USE THE TOOLS INSTEAD!")
        tool_descriptions.append("=== END TOOLS ===\n")

        return "\n".join(tool_descriptions)

    async def parse_and_execute_tools(self, response_text, original_message=None, model_name=None):
        """Parse tool calls from model response and execute them"""
        import re
        import json

        # Check if the model supports tools before processing
        if model_name and not memory_manager.model_supports_tools(model_name):
            logger.info(f"Model {model_name} does not support tools, skipping tool execution")
            return response_text, []

        # Pattern to match [TOOL:tool_name:{json_args}] or [TOOL:server.tool:{json_args}]
        # Use a more flexible pattern that handles JSON with quotes
        tool_pattern = r'\[TOOL:([^:]+):(\{.*?\})\]'
        tool_matches = re.findall(tool_pattern, response_text, re.DOTALL)

        logger.info(f"Tool parsing - Input: {response_text}")
        logger.info(f"Tool parsing - Pattern: {tool_pattern}")
        logger.info(f"Tool parsing - Matches: {tool_matches}")

        if not tool_matches:
            logger.info("No tool matches found")
            return response_text, []

        executed_tools = []
        tool_results_for_llm = []

        for tool_identifier, args_json in tool_matches:
            try:
                # Parse arguments
                arguments = json.loads(args_json)

                # Find the server and tool name for this tool
                server_name = None
                tool_name = None

                # Check if it's in server.tool format
                if '.' in tool_identifier:
                    server_name, tool_name = tool_identifier.split('.', 1)
                else:
                    # Find which server has this tool
                    for srv_name, srv_config in self.available_servers.items():
                        if srv_config.get('connected', False) and tool_identifier in srv_config.get('tools', []):
                            server_name = srv_name
                            tool_name = tool_identifier
                            break

                if not server_name or not tool_name:
                    logger.error(f"Could not find server for tool: {tool_identifier}")
                    continue

                # Execute tool
                try:
                    result = await self.execute_tool(server_name, tool_name, arguments)

                    if result['success']:
                        tool_result = ""
                        for item in result['result']:
                            if item['type'] == 'text':
                                tool_result += item['text'] + "\n"

                        executed_tools.append({
                            'tool': f"{server_name}.{tool_name}",
                            'arguments': arguments,
                            'result': tool_result.strip()
                        })

                        # Collect results for LLM processing
                        tool_results_for_llm.append({
                            'tool': f"{server_name}.{tool_name}",
                            'arguments': arguments,
                            'result': tool_result.strip()
                        })
                    else:
                        # Tool execution failed, but show that we tried
                        error_msg = f"Tool execution failed: {result.get('error', 'Unknown error')}"
                        executed_tools.append({
                            'tool': f"{server_name}.{tool_name}",
                            'arguments': arguments,
                            'result': error_msg
                        })

                        # Collect error for LLM processing too
                        tool_results_for_llm.append({
                            'tool': f"{server_name}.{tool_name}",
                            'arguments': arguments,
                            'result': error_msg
                        })

                except Exception as tool_error:
                    logger.error(f"Tool execution error: {tool_error}")
                    # Show that tool integration is working even if execution fails
                    error_msg = f"Tool integration working - execution failed due to: {str(tool_error)}"
                    executed_tools.append({
                        'tool': f"{server_name}.{tool_name}",
                        'arguments': arguments,
                        'result': error_msg
                    })

                    # Collect error for LLM processing
                    tool_results_for_llm.append({
                        'tool': f"{server_name}.{tool_name}",
                        'arguments': arguments,
                        'result': error_msg
                    })

            except Exception as e:
                logger.error(f"Error executing tool {tool_identifier}: {e}")
                error_msg = f"Failed to execute {tool_identifier}: {str(e)}"
                executed_tools.append({
                    'tool': tool_identifier,
                    'arguments': args_json,
                    'result': error_msg
                })
                tool_results_for_llm.append({
                    'tool': tool_identifier,
                    'arguments': args_json,
                    'result': error_msg
                })

        # If we have tool results and the necessary parameters, process with LLM
        if tool_results_for_llm and original_message and model_name:
            try:
                llm_processed_response = await self.process_tool_results_with_llm(
                    original_message,
                    tool_results_for_llm,
                    model_name
                )
                return llm_processed_response, executed_tools
            except Exception as e:
                logger.error(f"Error processing tool results with LLM: {e}")
                # Fallback to original behavior if LLM processing fails

        # Fallback: Replace tool calls with raw results (original behavior)
        modified_response = response_text
        for tool_identifier, args_json in tool_matches:
            tool_call = f"[TOOL:{tool_identifier}:{args_json}]"
            # Find corresponding result
            for tool_result in executed_tools:
                if tool_identifier in tool_result['tool']:
                    modified_response = modified_response.replace(
                        tool_call,
                        f"\n\n**Tool Result ({tool_result['tool']}):**\n{tool_result['result']}\n"
                    )
                    break

        return modified_response, executed_tools

    async def process_tool_results_with_llm(self, original_message, tool_results, model_name):
        """Have the LLM process and summarize tool results with enhanced formatting"""
        if not tool_results:
            return ""

        # Create a prompt for the LLM to process the tool results
        tool_summary = "Tool execution results:\n\n"
        for tool_result in tool_results:
            tool_summary += f"**Tool:** {tool_result['tool']}\n"
            tool_summary += f"**Arguments:** {tool_result['arguments']}\n"
            tool_summary += f"**Result:** {tool_result['result']}\n\n"

        # Create a prompt for the LLM to analyze and summarize with markdown formatting
        analysis_prompt = f"""The user asked: "{original_message}"

I used tools to gather information. Here are the raw tool results:

{tool_summary}

Please analyze these tool results and provide a comprehensive, well-structured response to the user's question.

Guidelines:
- Synthesize the information from the tool results
- Present the key findings clearly and concisely using markdown formatting
- Use proper markdown headers (##, ###), bullet points (*), bold (**text**), and italic (*text*) where appropriate
- Organize the information logically with clear sections
- Highlight the most relevant and important points
- If there are multiple search results, summarize the main themes and insights
- Make the response conversational and helpful
- Don't mention the tools or tool execution - just provide the final analysis
- Use markdown formatting for better readability (headers, lists, emphasis)
- Don't just list the raw results - provide analysis and context

Your response (use markdown formatting):"""

        try:
            # Use the same model to process the results
            response = ollama_client.generate_response(
                model_name,
                analysis_prompt,
                stream=False
            )

            return response.get('response', 'Failed to process tool results')

        except Exception as e:
            logger.error(f"Error processing tool results with LLM: {e}")
            # Fallback to basic formatting if LLM processing fails
            fallback_response = f"## Analysis for: {original_message}\n\n"
            for tool_result in tool_results:
                fallback_response += f"**{tool_result['tool']}:** {tool_result['result']}\n\n"
            return fallback_response

    def add_server(self, server_id, name, url, description):
        """Add a new MCP server"""
        # Add API key parameter if it's a Smithery server
        if 'server.smithery.ai' in url and 'api_key=' not in url and self.smithery_api_key:
            separator = '&' if '?' in url else '?'
            url = f"{url}{separator}api_key={self.smithery_api_key}&profile=dynamic-server"

        self.available_servers[server_id] = {
            'url': url,
            'description': description,
            'tools': [],  # Will be populated when connected
            'connected': False,
            'custom': True
        }

        # Save servers to file
        self.save_servers()

        logger.info(f"Added new MCP server: {server_id} ({name})")
        return True

    def disconnect_from_server(self, server_name):
        """Disconnect from an MCP server"""
        if server_name in self.connections:
            # Remove from connections
            del self.connections[server_name]

            # Update server status
            if server_name in self.available_servers:
                self.available_servers[server_name]['connected'] = False

            logger.info(f"Disconnected from MCP server: {server_name}")
            return True
        else:
            logger.warning(f"Server not connected: {server_name}")
            return False

    def delete_server(self, server_id):
        """Delete a custom MCP server"""
        if server_id in self.available_servers:
            server_config = self.available_servers[server_id]
            if server_config.get('custom', False):
                # Disconnect if connected
                if server_id in self.connections:
                    self.disconnect_from_server(server_id)

                # Remove from available servers
                del self.available_servers[server_id]

                # Save servers to file
                self.save_servers()

                logger.info(f"Deleted custom MCP server: {server_id}")
                return True
            else:
                logger.warning(f"Cannot delete built-in server: {server_id}")
                return False
        else:
            logger.warning(f"Server not found: {server_id}")
            return False

class OllamaClient:
    """Enhanced client for interacting with Ollama API"""
    
    def __init__(self, base_url=OLLAMA_BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 30
    
    def is_connected(self):
        """Check if Ollama is running and accessible"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama connection error: {e}")
            return False
    
    def get_models(self):
        """Fetch available models from Ollama"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            data = response.json()
            return {
                'success': True,
                'models': data.get('models', [])
            }
        except Exception as e:
            logger.error(f"Error fetching models: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_response(self, model, prompt, stream=True, images=None, context=None, messages=None):
        """Generate response from Ollama model with optional image support and conversation history"""
        try:
            # Use chat API if messages are provided (for conversation history)
            if messages:
                payload = {
                    'model': model,
                    'messages': messages,
                    'stream': stream
                }

                # Add images to the last message if provided
                if images and len(messages) > 0:
                    messages[-1]['images'] = images

                endpoint = f"{self.base_url}/api/chat"
            else:
                # Fallback to generate API for simple prompts
                payload = {
                    'model': model,
                    'prompt': prompt,
                    'stream': stream
                }

                # Add images if provided
                if images:
                    payload['images'] = images

                # Add context if provided
                if context:
                    payload['context'] = context

                endpoint = f"{self.base_url}/api/generate"

            response = self.session.post(
                endpoint,
                json=payload,
                stream=stream
            )
            response.raise_for_status()

            if stream:
                return response
            else:
                return response.json()

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            raise e

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def process_image(file_path):
    """Process uploaded image and convert to base64"""
    try:
        with Image.open(file_path) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # Resize if too large (max 1024x1024)
            max_size = (1024, 1024)
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Convert to base64
            buffer = io.BytesIO()
            img.save(buffer, format='JPEG', quality=85)
            img_data = buffer.getvalue()
            
            return base64.b64encode(img_data).decode('utf-8')
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        raise e

# Initialize clients
ollama_client = OllamaClient()
mcp_manager = MCPManager()
memory_manager = ChatMemoryManager()

# Chat sessions storage (in production, use a database)
chat_sessions = {}
current_session_id = None

# File paths for persistent storage
CHAT_SESSIONS_FILE = 'chat_sessions.json'

def load_chat_sessions():
    """Load chat sessions from file and restore memory manager state"""
    global chat_sessions
    try:
        if os.path.exists(CHAT_SESSIONS_FILE):
            with open(CHAT_SESSIONS_FILE, 'r') as f:
                chat_sessions = json.load(f)
            logger.info(f"Loaded {len(chat_sessions)} saved chat sessions")

            # Restore memory manager state for each session
            for session_id, session_data in chat_sessions.items():
                if 'messages' in session_data:
                    # Get the model name from the first message if available
                    model_name = None
                    if session_data['messages']:
                        model_name = session_data['messages'][0].get('model')

                    # Recreate the memory session
                    memory_manager.get_or_create_session(session_id, model_name)

                    # Restore conversation history
                    for msg in session_data['messages']:
                        if msg['role'] in ['user', 'assistant']:
                            memory_manager.add_message(session_id, msg['role'], msg['content'], model_name)

            logger.info(f"Restored memory for {len(chat_sessions)} sessions")
        else:
            chat_sessions = {}
    except Exception as e:
        logger.error(f"Error loading chat sessions: {e}")
        chat_sessions = {}

def save_chat_sessions():
    """Save chat sessions to file"""
    try:
        with open(CHAT_SESSIONS_FILE, 'w') as f:
            json.dump(chat_sessions, f, indent=2)
        logger.info(f"Saved {len(chat_sessions)} chat sessions")
    except Exception as e:
        logger.error(f"Error saving chat sessions: {e}")

def load_current_session():
    """Load current session ID from file"""
    global current_session_id
    try:
        current_session_file = 'current_session.txt'
        if os.path.exists(current_session_file):
            with open(current_session_file, 'r') as f:
                current_session_id = f.read().strip()
            logger.info(f"Loaded current session: {current_session_id}")
    except Exception as e:
        logger.error(f"Error loading current session: {e}")

def save_current_session():
    """Save current session ID to file"""
    try:
        current_session_file = 'current_session.txt'
        with open(current_session_file, 'w') as f:
            f.write(current_session_id or '')
        logger.info(f"Saved current session: {current_session_id}")
    except Exception as e:
        logger.error(f"Error saving current session: {e}")

def format_response_as_markdown(content):
    """Format response content as HTML from markdown"""
    try:
        # Convert markdown to HTML with basic extensions
        html_content = markdown.markdown(
            content,
            extensions=['fenced_code', 'tables', 'toc', 'nl2br']
        )
        return html_content
    except Exception as e:
        logger.error(f"Error formatting markdown: {e}")
        return content  # Return original content if formatting fails

@app.route('/')
def index():
    """Serve the main chat interface"""
    return render_template('index.html')

@app.route('/api/status')
def api_status():
    """Check system status"""
    ollama_connected = ollama_client.is_connected()
    mcp_servers = mcp_manager.get_available_servers()
    
    return jsonify({
        'ollama_connected': ollama_connected,
        'ollama_url': OLLAMA_BASE_URL,
        'mcp_servers': mcp_servers,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/models')
def api_models():
    """Get available models from Ollama"""
    result = ollama_client.get_models()
    if result['success']:
        return jsonify({
            'success': True,
            'models': result['models']
        })
    else:
        return jsonify({
            'success': False,
            'error': result['error']
        }), 500

@app.route('/api/load-model', methods=['POST'])
def api_load_model():
    """Load a model into Ollama's memory"""
    try:
        data = request.get_json()
        model_name = data.get('model')

        if not model_name:
            return jsonify({
                'success': False,
                'error': 'Model name is required'
            }), 400

        logger.info(f"Loading model {model_name} into memory...")

        # Use Ollama's generate endpoint with empty prompt to load model
        try:
            result = ollama_client.generate_response(
                model=model_name,
                prompt="",
                stream=False
            )


            # If we get here without exception, the model loaded successfully
            logger.info(f"Model {model_name} loaded successfully")
            return jsonify({
                'success': True,
                'message': f'Model {model_name} loaded successfully'
            })

        except Exception as e:
            error_msg = f'Failed to load model {model_name}: {str(e)}'
            logger.error(error_msg)
            return jsonify({
                'success': False,
                'error': error_msg
            }), 500

    except Exception as e:
        error_msg = f"Error loading model {model_name}: {str(e)}"
        logger.error(error_msg)
        return jsonify({
            'success': False,
            'error': error_msg
        }), 500

@app.route('/api/mcp/servers')
def api_mcp_servers():
    """Get available MCP servers"""
    return jsonify({
        'success': True,
        'servers': mcp_manager.get_available_servers()
    })

@app.route('/api/mcp/set-api-key', methods=['POST'])
def api_mcp_set_api_key():
    """Set Smithery API key for authentication"""
    data = request.get_json()
    if not data or 'api_key' not in data:
        return jsonify({
            'success': False,
            'error': 'API key is required'
        }), 400

    api_key = data['api_key'].strip()
    if not api_key:
        return jsonify({
            'success': False,
            'error': 'API key cannot be empty'
        }), 400

    mcp_manager.set_api_key(api_key)
    return jsonify({
        'success': True,
        'message': 'API key set successfully'
    })

@app.route('/api/mcp/registry', methods=['GET'])
def api_mcp_registry():
    """Get complete Smithery registry of available MCP servers"""
    async def get_registry():
        return await mcp_manager.fetch_smithery_registry()

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    result = loop.run_until_complete(get_registry())
    loop.close()

    if result['success']:
        return jsonify(result)
    else:
        return jsonify(result), 500

@app.route('/api/test-tool-parsing', methods=['POST'])
def test_tool_parsing():
    """Test tool parsing functionality"""
    data = request.get_json()
    test_response = data.get('response', '[TOOL:example_tool:{"query":"test"}]')

    # Test the tool parsing
    async def test_parse():
        return await mcp_manager.parse_and_execute_tools(test_response)

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    processed_response, executed_tools = loop.run_until_complete(test_parse())
    loop.close()

    return jsonify({
        'success': True,
        'original_response': test_response,
        'processed_response': processed_response,
        'tools_used': executed_tools
    })

@app.route('/api/mcp/add-server', methods=['POST'])
def api_mcp_add_server():
    """Add a new MCP server"""
    try:
        data = request.get_json()
        server_id = data.get('id')
        name = data.get('name')
        url = data.get('url')
        description = data.get('description')

        if not all([server_id, name, url]):
            return jsonify({
                'success': False,
                'error': 'Missing required fields: id, name, url'
            }), 400

        success = mcp_manager.add_server(server_id, name, url, description)

        if success:
            return jsonify({
                'success': True,
                'message': f'Server {name} added successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to add server'
            }), 500

    except Exception as e:
        logger.error(f"Add server error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/mcp/delete-server/<server_name>', methods=['DELETE'])
def api_mcp_delete_server(server_name):
    """Delete a custom MCP server"""
    try:
        success = mcp_manager.delete_server(server_name)

        if success:
            return jsonify({
                'success': True,
                'message': f'Server {server_name} deleted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Cannot delete built-in server or server not found'
            }), 400

    except Exception as e:
        logger.error(f"Delete server error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/mcp/connect/<server_name>', methods=['POST'])
def api_mcp_connect(server_name):
    """Connect to an MCP server"""
    async def connect():
        return await mcp_manager.connect_to_server(server_name)

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    result = loop.run_until_complete(connect())
    loop.close()

    if result['success']:
        return jsonify(result)
    else:
        return jsonify(result), 500

@app.route('/api/mcp/disconnect/<server_name>', methods=['POST'])
def api_mcp_disconnect(server_name):
    """Disconnect from an MCP server"""
    try:
        success = mcp_manager.disconnect_from_server(server_name)

        if success:
            return jsonify({
                'success': True,
                'message': f'Disconnected from {server_name}'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Server not found or not connected'
            }), 400

    except Exception as e:
        logger.error(f"Disconnect error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/search', methods=['POST'])
def api_search():
    """Perform web search using MCP"""
    try:
        data = request.get_json()
        query = data.get('query')

        if not query:
            return jsonify({
                'success': False,
                'error': 'Query is required'
            }), 400

        async def search():
            return await mcp_manager.execute_tool(
                'smithery_toolbox',
                'web_search',
                {'query': query, 'num_results': 5}
            )

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(search())
        loop.close()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Search error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/upload', methods=['POST'])
def api_upload():
    """Handle file uploads"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': 'File type not allowed'
            }), 400
        
        # Save file
        filename = secure_filename(file.filename)
        timestamp = str(int(time.time()))
        filename = f"{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # Process image
        base64_image = process_image(file_path)
        
        # Clean up file
        os.remove(file_path)
        
        return jsonify({
            'success': True,
            'image_data': base64_image,
            'filename': filename
        })
        
    except Exception as e:
        logger.error(f"Upload error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/chat', methods=['POST'])
def api_chat():
    """Handle enhanced chat requests with image and search support"""
    try:
        data = request.get_json()
        model = data.get('model')
        message = data.get('message')
        images = data.get('images', [])
        search_query = data.get('search_query')
        stream = data.get('stream', True)
        
        if not model or not message:
            return jsonify({
                'success': False,
                'error': 'Model and message are required'
            }), 400

        # Get session ID from request or use current session
        session_id = data.get('session_id')
        global current_session_id

        if session_id:
            # Use provided session ID
            current_session_id = session_id
        elif not current_session_id or current_session_id not in chat_sessions:
            # Create new session if none exists
            current_session_id = str(uuid.uuid4())

        # Ensure session exists in chat_sessions
        if current_session_id not in chat_sessions:
            chat_sessions[current_session_id] = {
                'title': f'Chat {current_session_id[:8]}',
                'created_at': datetime.now().isoformat(),
                'messages': []
            }
            save_chat_sessions()
            save_current_session()

        # Add user message to memory manager
        memory_manager.add_message(current_session_id, 'user', message, model)

        # Check context window status
        context_status = memory_manager.get_context_status(current_session_id, model)

        # Save user message to current session (for UI)
        user_message = {
            'role': 'user',
            'content': message,
            'timestamp': datetime.now().isoformat(),
            'has_image': bool(images),
            'model': model,
            'tokens': context_status['current_tokens'] if context_status else 0
        }
        chat_sessions[current_session_id]['messages'].append(user_message)
        save_chat_sessions()

        # Get conversation history from memory
        conversation_history = memory_manager.get_conversation_history(current_session_id)

        # Create enhanced prompt with context and tools (only for models that support tools)
        tools_info = mcp_manager.format_tools_for_prompt(model)

        # Add tools info to system message if we have conversation history
        if conversation_history and tools_info:
            for msg in conversation_history:
                if msg['role'] == 'system':
                    msg['content'] += f"\n\n{tools_info}"
                    break

        # Add current user message to conversation history
        current_user_message = {
            'role': 'user',
            'content': message
        }

        # Add images to current message if provided
        if images:
            current_user_message['images'] = images

        # Create messages array for chat API
        messages_for_api = conversation_history + [current_user_message]

        # Fallback enhanced prompt for generate API
        enhanced_prompt = message
        if tools_info:
            enhanced_prompt = tools_info + "\n" + enhanced_prompt

        # Add search results if search query provided
        if search_query:
            async def get_search_results():
                return await mcp_manager.execute_tool(
                    'duckduckgo',
                    'search_web',
                    {'query': search_query, 'max_results': 3}
                )

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            search_result = loop.run_until_complete(get_search_results())
            loop.close()

            if search_result['success']:
                search_context = f"\n\nWeb search results for '{search_query}':\n"
                for result in search_result['result']:
                    search_context += f"- {result.get('title', '')}: {result.get('snippet', '')}\n"
                enhanced_prompt = enhanced_prompt + search_context
        
        if stream:
            # Return streaming response
            def generate():
                full_response = ""
                context_status = None
                processed_response = ""
                try:
                    response = ollama_client.generate_response(
                        model,
                        enhanced_prompt,
                        stream=True,
                        images=images,
                        messages=messages_for_api
                    )
                    for line in response.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            try:
                                data = json.loads(decoded_line)

                                # Accumulate the response (handle both generate and chat API formats)
                                chunk_content = ""

                                if 'response' in data:
                                    chunk_content = data['response']
                                    full_response += chunk_content
                                elif 'message' in data and 'content' in data['message']:
                                    chunk_content = data['message']['content']
                                    full_response += chunk_content

                                # Send streaming chunk to frontend
                                if chunk_content:
                                    stream_data = {
                                        'model': model,
                                        'created_at': datetime.now().isoformat(),
                                        'response': chunk_content,
                                        'done': False
                                    }
                                    yield f"data: {json.dumps(stream_data)}\n\n"

                                # If this is the final chunk, process tools and save to session
                                if data.get('done', False):
                                    # Process any tool calls in the response
                                    async def process_tools():
                                        return await mcp_manager.parse_and_execute_tools(full_response, message, model)

                                    loop = asyncio.new_event_loop()
                                    asyncio.set_event_loop(loop)
                                    processed_response, executed_tools = loop.run_until_complete(process_tools())
                                    loop.close()

                                    # Add assistant response to memory (hide tool execution details)
                                    memory_manager.add_message(current_session_id, 'assistant', processed_response, model)

                                    # Get context status after adding response
                                    context_status = memory_manager.get_context_status(current_session_id, model)

                                    # Save the final processed response to session (for UI)
                                    assistant_message = {
                                        'role': 'assistant',
                                        'content': processed_response,
                                        'content_html': format_response_as_markdown(processed_response),
                                        'timestamp': datetime.now().isoformat(),
                                        'model': model,
                                        'tools_used': executed_tools if executed_tools else [],
                                        'tokens': context_status['current_tokens'] if context_status else 0,
                                        'context_status': context_status
                                    }
                                    chat_sessions[current_session_id]['messages'].append(assistant_message)
                                    save_chat_sessions()

                                    # Send final done message with context status and HTML content
                                    final_data = {
                                        'model': model,
                                        'created_at': datetime.now().isoformat(),
                                        'response': '',
                                        'response_html': format_response_as_markdown(processed_response),
                                        'done': True,
                                        'context_status': context_status
                                    }
                                    yield f"data: {json.dumps(final_data)}\n\n"
                            except json.JSONDecodeError:
                                continue
                except Exception as e:
                    error_data = {
                        'error': True,
                        'message': str(e)
                    }
                    yield f"data: {json.dumps(error_data)}\n\n"

            return Response(
                generate(),
                mimetype='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Access-Control-Allow-Origin': '*'
                }
            )
        else:
            # Return single response
            response = ollama_client.generate_response(
                model,
                enhanced_prompt,
                stream=False,
                images=images,
                messages=messages_for_api
            )

            # Handle both generate and chat API response formats
            response_text = response.get('response', '')
            if not response_text and 'message' in response:
                response_text = response['message'].get('content', '')

            # Process any tool calls in the response
            async def process_tools():
                return await mcp_manager.parse_and_execute_tools(response_text, message, model)

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            processed_response, executed_tools = loop.run_until_complete(process_tools())
            loop.close()

            # Add assistant response to memory (hide tool execution details)
            memory_manager.add_message(current_session_id, 'assistant', processed_response, model)

            # Get context status after adding response
            context_status = memory_manager.get_context_status(current_session_id, model)

            # Save assistant response to current session (for UI)
            assistant_message = {
                'role': 'assistant',
                'content': processed_response,
                'content_html': format_response_as_markdown(processed_response),
                'timestamp': datetime.now().isoformat(),
                'model': model,
                'tools_used': executed_tools if executed_tools else [],
                'tokens': context_status['current_tokens'] if context_status else 0,
                'context_status': context_status
            }
            chat_sessions[current_session_id]['messages'].append(assistant_message)
            save_chat_sessions()

            return jsonify({
                'success': True,
                'response': processed_response,
                'response_html': format_response_as_markdown(processed_response),
                'tools_used': executed_tools if executed_tools else [],
                'context_status': context_status,
                'should_start_new_session': context_status['should_start_new_session'] if context_status else False
            })
            
    except Exception as e:
        logger.error(f"Chat error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/model-info/<model_name>')
def api_model_info(model_name):
    """Get information about a specific model"""
    try:
        # Get capabilities from Ollama API
        capabilities = []
        supports_tools = False
        supports_images = False

        try:
            response = requests.post(f"{OLLAMA_BASE_URL}/api/show",
                                   json={"name": model_name},
                                   timeout=5)
            if response.status_code == 200:
                model_info = response.json()
                capabilities = model_info.get('capabilities', [])
                supports_tools = 'tools' in capabilities
                supports_images = 'vision' in capabilities
        except Exception as e:
            logger.warning(f"Could not get capabilities for {model_name}: {e}")
            # Fallback to name-based detection
            supports_images = 'vision' in model_name.lower() or 'llava' in model_name.lower()

        return jsonify({
            'success': True,
            'model': model_name,
            'info': {
                'name': model_name,
                'status': 'available',
                'description': f'Ollama model: {model_name}',
                'capabilities': capabilities,
                'supports_images': supports_images,
                'supports_tools': supports_tools
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/chat/sessions', methods=['GET'])
def api_chat_sessions():
    """Get all chat sessions"""
    sessions_list = []
    for session_id, session_data in chat_sessions.items():
        sessions_list.append({
            'id': session_id,
            'title': session_data.get('title', f'Chat {session_id[:8]}'),
            'created_at': session_data.get('created_at'),
            'message_count': len(session_data.get('messages', [])),
            'last_message': session_data.get('messages', [])[-1].get('content', '')[:50] if session_data.get('messages') else ''
        })

    return jsonify({
        'success': True,
        'sessions': sorted(sessions_list, key=lambda x: x['created_at'], reverse=True),
        'current_session': current_session_id
    })

@app.route('/api/chat/sessions', methods=['POST'])
def api_create_chat_session():
    """Create a new chat session"""
    global current_session_id
    import uuid

    session_id = str(uuid.uuid4())
    chat_sessions[session_id] = {
        'title': f'Chat {session_id[:8]}',
        'created_at': datetime.now().isoformat(),
        'messages': []
    }
    current_session_id = session_id
    save_chat_sessions()
    save_current_session()

    return jsonify({
        'success': True,
        'session_id': session_id,
        'message': 'New chat session created'
    })

@app.route('/api/chat/sessions/<session_id>', methods=['GET'])
def api_get_chat_session(session_id):
    """Get a specific chat session"""
    if session_id not in chat_sessions:
        return jsonify({
            'success': False,
            'error': 'Session not found'
        }), 404

    return jsonify({
        'success': True,
        'session': chat_sessions[session_id]
    })

@app.route('/api/chat/sessions/<session_id>', methods=['DELETE'])
def api_delete_chat_session(session_id):
    """Delete a chat session"""
    global current_session_id

    if session_id not in chat_sessions:
        return jsonify({
            'success': False,
            'error': 'Session not found'
        }), 404

    del chat_sessions[session_id]

    # If this was the current session, switch to another or create new
    if current_session_id == session_id:
        if chat_sessions:
            current_session_id = list(chat_sessions.keys())[0]
        else:
            current_session_id = None

    save_chat_sessions()
    save_current_session()

    return jsonify({
        'success': True,
        'message': 'Session deleted',
        'current_session': current_session_id
    })

@app.route('/api/chat/sessions/<session_id>/switch', methods=['POST'])
def api_switch_chat_session(session_id):
    """Switch to a different chat session"""
    global current_session_id

    if session_id not in chat_sessions:
        return jsonify({
            'success': False,
            'error': 'Session not found'
        }), 404

    current_session_id = session_id
    save_current_session()

    return jsonify({
        'success': True,
        'session_id': session_id,
        'messages': chat_sessions[session_id]['messages']
    })

@app.route('/api/chat/sessions/<session_id>/context-status', methods=['GET'])
def api_get_context_status(session_id):
    """Get context window status for a session"""
    # Get model from query parameter if provided
    model_name = request.args.get('model')
    context_status = memory_manager.get_context_status(session_id, model_name)

    if not context_status:
        return jsonify({
            'success': False,
            'error': 'Session not found'
        }), 404

    return jsonify({
        'success': True,
        'context_status': context_status
    })

@app.route('/api/chat/sessions/suggest-new', methods=['POST'])
def api_suggest_new_session():
    """Check if a new session should be started based on context window"""
    global current_session_id

    if not current_session_id:
        return jsonify({
            'success': True,
            'should_start_new': False,
            'message': 'No active session'
        })

    # Get model from request if provided
    model_name = request.json.get('model') if request.json else None
    context_status = memory_manager.get_context_status(current_session_id, model_name)

    if not context_status:
        return jsonify({
            'success': True,
            'should_start_new': False,
            'message': 'Session not found'
        })

    return jsonify({
        'success': True,
        'should_start_new': context_status['should_start_new_session'],
        'context_status': context_status,
        'message': 'Context window is nearly full. Consider starting a new session for better responses.' if context_status['should_start_new_session'] else 'Context window is healthy.'
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("=" * 60)
    print("🤖 OLLAMA ENHANCED CHAT SERVER")
    print("=" * 60)
    print(f"🌐 Server will run at: http://localhost:5001")
    print(f"🔗 Ollama URL: {OLLAMA_BASE_URL}")
    print(f"🔍 MCP Integration: Enabled (Real HTTP API)")
    print(f"📷 Image Support: Enabled")
    print("=" * 60)
    
    # Check Ollama connection on startup
    if ollama_client.is_connected():
        print("✅ Ollama is running and accessible")
    else:
        print("⚠️  Warning: Cannot connect to Ollama")
        print("💡 Make sure Ollama is running: ollama serve")
    
    print("=" * 60)
    print("🚀 Starting Flask server...")
    print("⌨️  Press Ctrl+C to stop")
    print("=" * 60)

    # Load persistent data
    load_chat_sessions()
    load_current_session()

    app.run(debug=True, host='0.0.0.0', port=5001, threaded=True)
